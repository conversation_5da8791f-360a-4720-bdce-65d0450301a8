<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>除法计算过程演示器</title>
    <!-- 引入第三方库 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mathjs/11.11.0/math.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fraction.js/4.3.7/fraction.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .main-content {
            display: flex;
            min-height: 600px;
        }

        .input-panel {
            flex: 1;
            padding: 30px;
            background: #f8f9fa;
            border-right: 2px solid #e9ecef;
        }

        .calculation-panel {
            flex: 2;
            padding: 30px;
            background: white;
        }

        .input-group {
            margin-bottom: 25px;
        }

        .input-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
            font-size: 1.1em;
        }

        .input-group input {
            width: 100%;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 1.2em;
            transition: all 0.3s ease;
        }

        .input-group input:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 0 10px rgba(76, 175, 80, 0.3);
        }

        .calculate-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.3em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .calculate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
        }

        .clear-btn {
            width: 100%;
            padding: 12px;
            background: #f44336;
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .clear-btn:hover {
            background: #d32f2f;
            transform: translateY(-1px);
        }

        .division-display {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border: 2px solid #e9ecef;
            overflow-x: auto;
        }

        .division-layout {
            font-family: 'Courier New', monospace;
            font-size: 1.2em;
            line-height: 1.6;
            text-align: left;
            color: #333;
            white-space: pre;
            min-width: 400px;
        }

        .vertical-division {
            display: inline-block;
            border: 2px solid #333;
            border-radius: 10px;
            padding: 20px;
            background: white;
            margin: 10px;
        }

        .quotient-line {
            border-bottom: 2px solid #333;
            padding-bottom: 5px;
            margin-bottom: 10px;
            font-weight: bold;
            color: #2e7d32;
        }

        .dividend-line {
            margin: 10px 0;
            position: relative;
        }

        .step-highlight {
            background: #ffeb3b;
            padding: 2px 4px;
            border-radius: 3px;
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0% { background-color: #ffeb3b; }
            50% { background-color: #ffc107; }
            100% { background-color: #ffeb3b; }
        }

        .calculation-step {
            margin: 15px 0;
            padding: 10px;
            background: #e8f5e8;
            border-radius: 8px;
            border-left: 4px solid #4CAF50;
        }

        .fraction-display {
            font-size: 1.5em;
            text-align: center;
            margin: 20px 0;
        }

        .fraction-display .numerator {
            display: block;
            border-bottom: 2px solid #333;
            padding-bottom: 5px;
        }

        .fraction-display .denominator {
            display: block;
            padding-top: 5px;
        }

        .steps-container {
            background: white;
            border-radius: 15px;
            padding: 25px;
            border: 2px solid #e9ecef;
        }

        .step {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #4CAF50;
            opacity: 0;
            transform: translateX(-20px);
        }

        .step.active {
            background: #e8f5e8;
            border-left-color: #2e7d32;
        }

        .step-number {
            display: inline-block;
            background: #4CAF50;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            text-align: center;
            line-height: 30px;
            margin-right: 15px;
            font-weight: bold;
        }

        .step-content {
            display: inline-block;
            vertical-align: top;
            width: calc(100% - 50px);
        }

        .highlight {
            background: #ffeb3b;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }

        .error-message {
            background: #ffebee;
            color: #c62828;
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #f44336;
            margin-bottom: 20px;
            display: none;
        }

        .result-summary {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin-top: 20px;
            font-size: 1.3em;
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .main-content {
                flex-direction: column;
            }
            
            .input-panel {
                border-right: none;
                border-bottom: 2px solid #e9ecef;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header animate__animated animate__fadeInDown">
            <h1><i class="fas fa-calculator"></i> 除法计算过程演示器</h1>
            <p>输入被除数和除数，观看详细的除法计算过程</p>
        </div>

        <div class="main-content">
            <div class="input-panel animate__animated animate__fadeInLeft">
                <div class="input-group">
                    <label for="dividend"><i class="fas fa-arrow-down"></i> 被除数</label>
                    <input type="number" id="dividend" placeholder="请输入被除数" value="1234">
                </div>

                <div class="input-group">
                    <label for="divisor"><i class="fas fa-arrow-right"></i> 除数</label>
                    <input type="number" id="divisor" placeholder="请输入除数" value="56">
                </div>

                <div class="input-group">
                    <label for="precision"><i class="fas fa-decimal"></i> 小数位数</label>
                    <select id="precision" style="width: 100%; padding: 15px; border: 2px solid #ddd; border-radius: 10px; font-size: 1.2em;">
                        <option value="0">整数除法</option>
                        <option value="2" selected>保留2位小数</option>
                        <option value="4">保留4位小数</option>
                        <option value="6">保留6位小数</option>
                        <option value="fraction">分数形式</option>
                    </select>
                </div>

                <div class="input-group">
                    <label><i class="fas fa-cogs"></i> 显示选项</label>
                    <div style="display: flex; flex-direction: column; gap: 10px; margin-top: 10px;">
                        <label style="display: flex; align-items: center; font-weight: normal;">
                            <input type="checkbox" id="showSteps" checked style="margin-right: 10px; transform: scale(1.2);">
                            显示详细步骤
                        </label>
                        <label style="display: flex; align-items: center; font-weight: normal;">
                            <input type="checkbox" id="showAnimation" checked style="margin-right: 10px; transform: scale(1.2);">
                            启用动画效果
                        </label>
                        <label style="display: flex; align-items: center; font-weight: normal;">
                            <input type="checkbox" id="showVertical" checked style="margin-right: 10px; transform: scale(1.2);">
                            显示竖式计算
                        </label>
                    </div>
                </div>

                <button class="calculate-btn" onclick="performDivision()">
                    <i class="fas fa-play"></i> 开始计算
                </button>

                <button class="clear-btn" onclick="clearAll()">
                    <i class="fas fa-trash"></i> 清空
                </button>

                <div class="input-group">
                    <label><i class="fas fa-lightbulb"></i> 示例</label>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-top: 10px;">
                        <button onclick="loadExample(1234, 56)" style="padding: 8px; border: 1px solid #ddd; border-radius: 5px; background: white; cursor: pointer; font-size: 0.9em;">1234 ÷ 56</button>
                        <button onclick="loadExample(999, 37)" style="padding: 8px; border: 1px solid #ddd; border-radius: 5px; background: white; cursor: pointer; font-size: 0.9em;">999 ÷ 37</button>
                        <button onclick="loadExample(2468, 123)" style="padding: 8px; border: 1px solid #ddd; border-radius: 5px; background: white; cursor: pointer; font-size: 0.9em;">2468 ÷ 123</button>
                        <button onclick="loadExample(7890, 234)" style="padding: 8px; border: 1px solid #ddd; border-radius: 5px; background: white; cursor: pointer; font-size: 0.9em;">7890 ÷ 234</button>
                    </div>
                </div>

                <div class="error-message" id="errorMessage">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span id="errorText"></span>
                </div>
            </div>

            <div class="calculation-panel animate__animated animate__fadeInRight">
                <div class="division-display" id="divisionDisplay">
                    <div class="division-layout" id="divisionLayout">
                        请输入数字开始计算
                    </div>
                </div>

                <div class="steps-container">
                    <h3><i class="fas fa-list-ol"></i> 计算步骤</h3>
                    <div id="stepsContainer">
                        <p style="text-align: center; color: #666; margin-top: 20px;">
                            计算步骤将在这里显示
                        </p>
                    </div>
                </div>

                <div class="result-summary" id="resultSummary" style="display: none;">
                    <i class="fas fa-check-circle"></i>
                    <span id="finalResult"></span>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            const errorText = document.getElementById('errorText');
            errorText.textContent = message;
            errorDiv.style.display = 'block';
            
            gsap.from(errorDiv, {
                duration: 0.5,
                y: -20,
                opacity: 0,
                ease: "bounce.out"
            });
        }

        function hideError() {
            document.getElementById('errorMessage').style.display = 'none';
        }

        function clearAll() {
            document.getElementById('dividend').value = '';
            document.getElementById('divisor').value = '';
            document.getElementById('divisionLayout').innerHTML = '请输入数字开始计算';
            document.getElementById('stepsContainer').innerHTML = '<p style="text-align: center; color: #666; margin-top: 20px;">计算步骤将在这里显示</p>';
            document.getElementById('resultSummary').style.display = 'none';
            hideError();
        }

        function performDivision() {
            hideError();

            const dividend = parseFloat(document.getElementById('dividend').value);
            const divisor = parseFloat(document.getElementById('divisor').value);
            const precision = document.getElementById('precision').value;
            const showSteps = document.getElementById('showSteps').checked;
            const showAnimation = document.getElementById('showAnimation').checked;
            const showVertical = document.getElementById('showVertical').checked;

            // 输入验证
            if (isNaN(dividend) || isNaN(divisor)) {
                showError('请输入有效的数字！');
                return;
            }

            if (divisor === 0) {
                showError('除数不能为0！');
                return;
            }

            if (dividend < 0 || divisor < 0) {
                showError('暂时只支持正数除法！');
                return;
            }

            // 根据精度类型执行不同的计算
            if (precision === 'fraction') {
                performFractionDivision(dividend, divisor, showSteps, showAnimation, showVertical);
            } else {
                performLongDivision(dividend, divisor, parseInt(precision), showSteps, showAnimation, showVertical);
            }
        }

        function performFractionDivision(dividend, divisor, showSteps, showAnimation, showVertical) {
            try {
                // 使用Fraction.js库进行精确分数计算
                const dividendFraction = new Fraction(dividend);
                const divisorFraction = new Fraction(divisor);
                const result = dividendFraction.div(divisorFraction);

                const steps = [];

                // 显示分数形式的除法
                const layout = `
                    <div class="fraction-display">
                        <div class="numerator">${dividendFraction.toFraction()}</div>
                        <div style="text-align: center; font-size: 1.2em; margin: 10px 0;">÷</div>
                        <div class="denominator">${divisorFraction.toFraction()}</div>
                        <div style="text-align: center; font-size: 1.2em; margin: 10px 0;">=</div>
                        <div class="numerator" style="color: #4CAF50; font-weight: bold;">${result.toFraction()}</div>
                    </div>
                `;

                document.getElementById('divisionLayout').innerHTML = layout;

                if (showSteps) {
                    steps.push({
                        step: 1,
                        description: `将除法转换为分数形式`,
                        detail: `${dividend} ÷ ${divisor} = ${dividendFraction.toFraction()} ÷ ${divisorFraction.toFraction()}`
                    });

                    steps.push({
                        step: 2,
                        description: `除法转换为乘法`,
                        detail: `${dividendFraction.toFraction()} ÷ ${divisorFraction.toFraction()} = ${dividendFraction.toFraction()} × ${divisorFraction.inverse().toFraction()}`
                    });

                    steps.push({
                        step: 3,
                        description: `计算结果`,
                        detail: `${dividendFraction.toFraction()} × ${divisorFraction.inverse().toFraction()} = ${result.toFraction()}`
                    });

                    if (result.d !== 1) {
                        steps.push({
                            step: 4,
                            description: `小数形式`,
                            detail: `${result.toFraction()} = ${result.valueOf()}`
                        });
                    }
                }

                displayStepsWithAnimation(steps, result.toFraction(), 0, dividend, divisor, showAnimation);

            } catch (error) {
                showError('分数计算出错：' + error.message);
            }
        }

        function performLongDivision(dividend, divisor, precision, showSteps, showAnimation, showVertical) {
            const steps = [];
            const quotientDigits = [];
            let currentDividend = 0;
            let position = 0;
            let hasDecimalPoint = false;
            let decimalPlaces = 0;

            // 处理整数部分
            const dividendStr = Math.floor(dividend).toString();

            if (showVertical) {
                displayVerticalDivisionLayout(dividend, divisor);
            } else {
                displayDivisionLayout(dividend, divisor);
            }

            // 执行长除法过程 - 整数部分
            while (position < dividendStr.length || (precision > 0 && decimalPlaces < precision && currentDividend !== 0)) {
                if (position < dividendStr.length) {
                    // 处理整数位
                    currentDividend = currentDividend * 10 + parseInt(dividendStr[position]);
                } else if (!hasDecimalPoint && precision > 0) {
                    // 开始处理小数部分
                    hasDecimalPoint = true;
                    if (quotientDigits.length === 0) quotientDigits.push(0);
                    quotientDigits.push('.');
                    currentDividend = currentDividend * 10;
                    decimalPlaces++;
                } else if (hasDecimalPoint && decimalPlaces < precision) {
                    // 继续处理小数位
                    currentDividend = currentDividend * 10;
                    decimalPlaces++;
                } else {
                    break;
                }

                if (currentDividend >= divisor || (quotientDigits.length > 0 && !hasDecimalPoint)) {
                    const quotientDigit = Math.floor(currentDividend / divisor);
                    const remainder = currentDividend % divisor;
                    const product = quotientDigit * divisor;

                    if (currentDividend >= divisor || quotientDigits.length > 0) {
                        quotientDigits.push(quotientDigit);
                    }

                    if (showSteps) {
                        let stepDescription, stepDetail;

                        if (hasDecimalPoint) {
                            stepDescription = `小数位：${currentDividend} ÷ ${divisor} = ${quotientDigit} 余 ${remainder}`;
                            stepDetail = `${currentDividend} ÷ ${divisor} = ${quotientDigit}，${quotientDigit} × ${divisor} = ${product}，${currentDividend} - ${product} = ${remainder}`;
                        } else {
                            stepDescription = `${currentDividend} ÷ ${divisor} = ${quotientDigit} 余 ${remainder}`;
                            stepDetail = `${currentDividend} ÷ ${divisor} = ${quotientDigit}，${quotientDigit} × ${divisor} = ${product}，${currentDividend} - ${product} = ${remainder}`;
                        }

                        steps.push({
                            step: steps.length + 1,
                            description: stepDescription,
                            detail: stepDetail,
                            currentDividend: currentDividend,
                            quotient: quotientDigit,
                            remainder: remainder,
                            product: product,
                            isDecimal: hasDecimalPoint
                        });
                    }

                    currentDividend = remainder;
                } else if (quotientDigits.length > 0 && !hasDecimalPoint) {
                    quotientDigits.push(0);
                    if (showSteps) {
                        steps.push({
                            step: steps.length + 1,
                            description: `${currentDividend} < ${divisor}，商为 0`,
                            detail: `因为 ${currentDividend} 小于 ${divisor}，所以这一位的商是 0`,
                            currentDividend: currentDividend,
                            quotient: 0,
                            remainder: currentDividend
                        });
                    }
                }

                if (position < dividendStr.length) {
                    position++;
                }
            }

            // 处理最终结果
            let finalQuotient = quotientDigits.join('') || '0';
            if (finalQuotient.endsWith('.')) {
                finalQuotient = finalQuotient.slice(0, -1);
            }
            const finalRemainder = currentDividend;

            // 动画显示步骤
            displayStepsWithAnimation(steps, finalQuotient, finalRemainder, dividend, divisor, showAnimation);
        }

        function displayDivisionLayout(dividend, divisor) {
            const layout = `
                <div style="border-bottom: 2px solid #333; display: inline-block; padding-bottom: 10px;">
                    ${divisor} ) ${dividend}
                </div>
            `;
            document.getElementById('divisionLayout').innerHTML = layout;
        }

        function displayVerticalDivisionLayout(dividend, divisor) {
            const dividendStr = dividend.toString();
            const divisorStr = divisor.toString();

            // 创建竖式除法布局
            let layout = `<div class="vertical-division">`;
            layout += `<div class="quotient-line" id="quotientDisplay">商：</div>`;
            layout += `<div style="display: flex; align-items: center;">`;
            layout += `<div style="margin-right: 10px;">${divisorStr}</div>`;
            layout += `<div style="border-left: 2px solid #333; border-top: 2px solid #333; padding: 5px 10px;">`;
            layout += `<div class="dividend-line">${dividendStr}</div>`;
            layout += `<div id="calculationSteps" style="min-height: 100px;"></div>`;
            layout += `</div>`;
            layout += `</div>`;
            layout += `</div>`;

            document.getElementById('divisionLayout').innerHTML = layout;
        }

        function displayStepsWithAnimation(steps, quotient, remainder, dividend, divisor, showAnimation) {
            const container = document.getElementById('stepsContainer');
            container.innerHTML = '';

            if (!steps || steps.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: #666; margin-top: 20px;">无详细步骤</p>';
                showFinalResult(dividend, divisor, quotient, remainder);
                return;
            }

            const delay = showAnimation ? 800 : 0;

            steps.forEach((step, index) => {
                setTimeout(() => {
                    const stepDiv = document.createElement('div');
                    stepDiv.className = 'step';

                    let stepContent = `
                        <span class="step-number">${step.step}</span>
                        <div class="step-content">
                            <strong>${step.description}</strong><br>
                            <small>${step.detail}</small>
                    `;

                    // 如果有乘法验证，显示验证过程
                    if (step.product !== undefined) {
                        stepContent += `<br><small style="color: #666;">验证：${step.quotient} × ${divisor} = ${step.product}</small>`;
                    }

                    stepContent += `</div>`;
                    stepDiv.innerHTML = stepContent;

                    container.appendChild(stepDiv);

                    if (showAnimation) {
                        // GSAP动画
                        gsap.to(stepDiv, {
                            duration: 0.5,
                            opacity: 1,
                            x: 0,
                            ease: "back.out(1.7)"
                        });
                    } else {
                        stepDiv.style.opacity = '1';
                        stepDiv.style.transform = 'translateX(0)';
                    }

                    // 如果是最后一步，显示最终结果
                    if (index === steps.length - 1) {
                        setTimeout(() => {
                            showFinalResult(dividend, divisor, quotient, remainder);
                        }, showAnimation ? 500 : 100);
                    }
                }, index * delay);
            });
        }

        function showFinalResult(dividend, divisor, quotient, remainder) {
            const resultDiv = document.getElementById('resultSummary');
            const resultText = document.getElementById('finalResult');

            let resultString = `${dividend} ÷ ${divisor} = ${quotient}`;
            if (remainder > 0 && !quotient.includes('.') && !quotient.includes('/')) {
                resultString += ` 余 ${remainder}`;
            }

            // 如果是小数结果，也显示分数形式
            if (quotient.includes('.')) {
                try {
                    const fraction = new Fraction(parseFloat(quotient));
                    if (fraction.d !== 1) {
                        resultString += ` (${fraction.toFraction()})`;
                    }
                } catch (e) {
                    // 忽略分数转换错误
                }
            }

            resultText.textContent = resultString;
            resultDiv.style.display = 'block';

            const showAnimation = document.getElementById('showAnimation').checked;
            if (showAnimation) {
                gsap.from(resultDiv, {
                    duration: 0.8,
                    scale: 0.8,
                    opacity: 0,
                    ease: "elastic.out(1, 0.5)"
                });
            }
        }

        // 添加示例按钮功能
        function loadExample(dividend, divisor) {
            document.getElementById('dividend').value = dividend;
            document.getElementById('divisor').value = divisor;
            performDivision();
        }

        // 回车键触发计算
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performDivision();
            }
        });

        // 页面加载完成后的动画
        window.addEventListener('load', function() {
            gsap.from('.container', {
                duration: 1,
                y: 50,
                opacity: 0,
                ease: "power2.out"
            });
        });
    </script>
</body>
</html>
