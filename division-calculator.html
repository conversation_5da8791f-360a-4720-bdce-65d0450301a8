<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>除法计算过程演示器</title>
    <!-- 引入第三方库 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto+Mono:wght@300;400;500;700&family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans SC', 'Roboto Mono', monospace;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            padding: 30px;
            text-align: center;
            color: white;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .input-section {
            padding: 40px;
            background: white;
        }

        .input-group {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .input-field {
            position: relative;
        }

        .input-field input {
            width: 120px;
            height: 60px;
            border: 3px solid #e0e0e0;
            border-radius: 15px;
            text-align: center;
            font-size: 1.5em;
            font-weight: 500;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .input-field input:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 20px rgba(79, 172, 254, 0.3);
            background: white;
        }

        .operator {
            font-size: 2em;
            font-weight: bold;
            color: #4facfe;
        }

        .calculate-btn {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 50px;
            font-size: 1.2em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 10px 20px rgba(255, 107, 107, 0.3);
        }

        .calculate-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(255, 107, 107, 0.4);
        }

        .result-section {
            padding: 40px;
            background: #f8f9fa;
            border-top: 1px solid #e0e0e0;
        }

        .division-process {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            font-family: 'Roboto Mono', monospace;
        }

        .process-title {
            text-align: center;
            font-size: 1.5em;
            color: #4facfe;
            margin-bottom: 30px;
            font-weight: 600;
        }

        .division-layout {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
        }

        .division-symbol {
            position: relative;
            display: inline-block;
            font-size: 1.5em;
            line-height: 1.2;
        }

        .dividend {
            font-size: 1.8em;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .divisor-quotient {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 20px;
        }

        .divisor {
            font-size: 1.5em;
            font-weight: 600;
            color: #e74c3c;
            padding: 10px 15px;
            background: rgba(231, 76, 60, 0.1);
            border-radius: 10px;
        }

        .quotient {
            font-size: 1.8em;
            font-weight: 600;
            color: #27ae60;
            padding: 10px 15px;
            background: rgba(39, 174, 96, 0.1);
            border-radius: 10px;
        }

        .steps {
            width: 100%;
            max-width: 600px;
        }

        .step {
            margin: 15px 0;
            padding: 15px;
            background: rgba(79, 172, 254, 0.05);
            border-left: 4px solid #4facfe;
            border-radius: 0 10px 10px 0;
            font-size: 1.1em;
            line-height: 1.6;
        }

        .step-number {
            color: #4facfe;
            font-weight: 600;
            margin-right: 10px;
        }

        .remainder {
            font-size: 1.3em;
            color: #f39c12;
            font-weight: 600;
            text-align: center;
            margin-top: 20px;
            padding: 15px;
            background: rgba(243, 156, 18, 0.1);
            border-radius: 10px;
        }

        .error {
            color: #e74c3c;
            text-align: center;
            font-size: 1.2em;
            padding: 20px;
            background: rgba(231, 76, 60, 0.1);
            border-radius: 10px;
            margin: 20px 0;
        }

        @media (max-width: 768px) {
            .input-group {
                flex-direction: column;
                gap: 15px;
            }
            
            .divisor-quotient {
                flex-direction: column;
                gap: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-calculator"></i> 除法计算过程演示器</h1>
            <p>输入被除数和除数，查看详细的除法计算过程</p>
        </div>

        <div class="input-section">
            <div class="input-group">
                <div class="input-field">
                    <input type="number" id="dividend" placeholder="被除数" min="1">
                </div>
                <span class="operator">÷</span>
                <div class="input-field">
                    <input type="number" id="divisor" placeholder="除数" min="1">
                </div>
                <span class="operator">=</span>
                <button class="calculate-btn" onclick="calculateDivision()">
                    <i class="fas fa-play"></i> 开始计算
                </button>
            </div>
        </div>

        <div class="result-section" id="resultSection" style="display: none;">
            <div class="division-process">
                <div class="process-title">
                    <i class="fas fa-cogs"></i> 计算过程
                </div>
                <div id="divisionResult"></div>
            </div>
        </div>
    </div>

    <script>
        function calculateDivision() {
            const dividend = parseInt(document.getElementById('dividend').value);
            const divisor = parseInt(document.getElementById('divisor').value);
            const resultSection = document.getElementById('resultSection');
            const resultDiv = document.getElementById('divisionResult');

            // 输入验证
            if (!dividend || !divisor) {
                resultDiv.innerHTML = '<div class="error"><i class="fas fa-exclamation-triangle"></i> 请输入有效的被除数和除数</div>';
                resultSection.style.display = 'block';
                return;
            }

            if (divisor === 0) {
                resultDiv.innerHTML = '<div class="error"><i class="fas fa-ban"></i> 除数不能为0</div>';
                resultSection.style.display = 'block';
                return;
            }

            // 计算结果
            const quotient = Math.floor(dividend / divisor);
            const remainder = dividend % divisor;

            // 生成计算过程
            let html = `
                <div class="division-layout">
                    <div class="dividend">${dividend}</div>
                    <div class="divisor-quotient">
                        <div class="divisor">÷ ${divisor}</div>
                        <div class="quotient">= ${quotient}</div>
                    </div>
                </div>
                
                <div class="steps">
                    <div class="step">
                        <span class="step-number">步骤 1:</span>
                        确定被除数 ${dividend} 和除数 ${divisor}
                    </div>
            `;

            // 生成详细步骤
            if (dividend >= divisor) {
                let currentDividend = dividend;
                let stepCount = 2;
                let totalQuotient = 0;

                while (currentDividend >= divisor) {
                    const currentQuotient = Math.floor(currentDividend / divisor);
                    const currentRemainder = currentDividend % divisor;
                    
                    html += `
                        <div class="step">
                            <span class="step-number">步骤 ${stepCount}:</span>
                            ${currentDividend} ÷ ${divisor} = ${currentQuotient}，余数 ${currentRemainder}
                        </div>
                    `;
                    
                    totalQuotient += currentQuotient;
                    currentDividend = currentRemainder;
                    stepCount++;
                    
                    if (currentRemainder === 0) break;
                    if (currentRemainder < divisor) break;
                }

                html += `
                    <div class="step">
                        <span class="step-number">步骤 ${stepCount}:</span>
                        最终结果：商 = ${quotient}${remainder > 0 ? `，余数 = ${remainder}` : ''}
                    </div>
                `;
            } else {
                html += `
                    <div class="step">
                        <span class="step-number">步骤 2:</span>
                        因为被除数 ${dividend} 小于除数 ${divisor}，所以商为 0，余数为 ${dividend}
                    </div>
                `;
            }

            html += '</div>';

            if (remainder > 0) {
                html += `
                    <div class="remainder">
                        <i class="fas fa-info-circle"></i> 
                        余数: ${remainder}
                    </div>
                `;
            }

            resultDiv.innerHTML = html;
            resultSection.style.display = 'block';
            
            // 平滑滚动到结果区域
            resultSection.scrollIntoView({ behavior: 'smooth' });
        }

        // 回车键触发计算
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                calculateDivision();
            }
        });

        // 输入框焦点效果
        document.querySelectorAll('input').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'scale(1.05)';
            });
            
            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'scale(1)';
            });
        });
    </script>
</body>
</html>
